// Simple plugin setup to avoid CommonJS/ES6 issues
import "vue-toastification/dist/index.css"

export default defineNuxtPlugin(async (nuxtApp) => {
  if (import.meta.client) {
    try {
      // Dynamic import to handle CommonJS/ES6 compatibility
      const vueToastification = await import('vue-toastification')
      const Toast = vueToastification.default || vueToastification
      const POSITION = vueToastification.POSITION
      const TYPE = vueToastification.TYPE

      // Configure toast options
      const toastOptions = {
        position: POSITION.TOP_RIGHT,
        timeout: 3000,
        closeOnClick: true,
        pauseOnFocusLoss: true,
        pauseOnHover: true,
        draggable: true,
        draggablePercent: 0.6,
        showCloseButtonOnHover: false,
        hideProgressBar: false,
        closeButton: "button",
        icon: true,
        rtl: false,
        transition: "Vue-Toastification__bounce",
        maxToasts: 5,
        newestOnTop: true,

        // Custom styling
        toastDefaults: {
          [TYPE.SUCCESS]: {
            timeout: 3000,
            hideProgressBar: false,
          },
          [TYPE.ERROR]: {
            timeout: 5000,
            hideProgressBar: false,
          },
          [TYPE.INFO]: {
            timeout: 3000,
            hideProgressBar: false,
          },
          [TYPE.WARNING]: {
            timeout: 4000,
            hideProgressBar: false,
          }
        }
      }

      // Install toast plugin
      nuxtApp.vueApp.use(Toast, toastOptions)
    } catch (error) {
      console.warn('Failed to initialize vue-toastification:', error)
    }
  }
})
