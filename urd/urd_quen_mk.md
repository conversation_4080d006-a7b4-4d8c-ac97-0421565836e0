# **User Requirements Document (URD) - Giao diện Quên mật khẩu**

## **1. Tổng quan**

Tài liệu mô tả yêu cầu người dùng cho chức năng Quên mật khẩu trong hệ thống.

## **2. User Flow**

### **2.1 Flow chính**

- **Bước 1:** Tại giao diện Đăng nhập, ng<PERSON><PERSON><PERSON> dùng click "Quên mật khẩu"
- **Bước 2:** Hệ thống hiển thị popup Quên mật khẩu
- **Bước 3:** Người dùng nhập địa chỉ email và click "Lấy lại mật khẩu"


## **3. Y<PERSON>u cầu Giao diện**

### **3.1 Tiêu đề giao diện**

- **Tên:** <PERSON>uên mật khẩu
- **Loại:** Popup/Modal


### **3.2 Dòng hướng dẫn**

- **Nội dung:** "Vui lòng nhập địa chỉ email bạn đã đăng ký tài khoản"
- **Vị trí:** Hiển thị dưới tiêu đề


### **3.3 Trường Email**

- **Loại:** Textbox (bắt buộc)
- **Placeholder:** "Nhập Email"
- **Validation:**
    - Chỉ chấp nhận định dạng email hợp lệ
    - Hiển thị lỗi inline: *"Email chưa đúng định dạng"* khi sai format


### **3.4 Nút Lấy lại mật khẩu**

- **Loại:** Button (Primary)
- **Chức năng:** Gửi yêu cầu reset mật khẩu

## **4. Yêu cầu Xử lý**

### **4.1 Trường hợp thành công**

- Hệ thống gửi mật khẩu mới qua email
- Hiển thị toast thông báo: *"Mật khẩu mới đã được gửi đến địa chỉ email của bạn. Vui lòng kiểm tra email và sử dụng mật khẩu mới để đăng nhập."*


### **4.2 Trường hợp lỗi**

- Email chưa đăng ký: Hiển thị toast *"Email này chưa được đăng ký. Vui lòng kiểm tra lại hoặc đăng ký tài khoản mới."*


## **5. Yêu cầu Email**

### **5.1 Thông tin Email**

- **Subject:** `[Tên hệ thống] Lấy lại mật khẩu`
- **Recipient:** Email người dùng nhập


### **5.2 Nội dung Email**

```
Bạn vừa yêu cầu lấy lại mật khẩu cho tài khoản trên [Tên hệ thống].

Dưới đây là mật khẩu mới của bạn:
🔐 Mật khẩu mới: [Generated Password]

Vui lòng sử dụng mật khẩu này để đăng nhập vào hệ thống.
**Để đảm bảo an toàn thông tin, bạn nên đổi mật khẩu ngay sau khi đăng nhập.**

Trân trọng!
```


## **6. Yêu cầu Kỹ thuật**

### **6.1 Sinh mật khẩu**

- Mật khẩu được tạo tự động phải tuân thủ quy định bảo mật của hệ thống
- Độ dài: 6-16 ký tự
- Bao gồm: chữ hoa, chữ thường, số và ký tự đặc biệt


### **6.2 Bảo mật**

- Mật khẩu mới phải được mã hóa trước khi lưu vào database
- Log lại hoạt động reset mật khẩu để audit


## **7. Acceptance Criteria**

- [ ] Popup hiển thị đúng khi click "Quên mật khẩu"
- [ ] Validation email hoạt động chính xác
- [ ] Toast message hiển thị đúng cho từng trường hợp
- [ ] Email được gửi thành công với nội dung đúng format
- [ ] Mật khẩu mới được tạo theo đúng quy định

