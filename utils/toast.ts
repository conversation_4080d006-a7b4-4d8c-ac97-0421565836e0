// Toast utility to handle CommonJS/ES6 compatibility issues
let toastInstance: any = null

// Initialize toast on client side
const initToast = async () => {
  if (import.meta.client && !toastInstance) {
    try {
      // Dynamic import to handle CommonJS/ES6 compatibility
      const vueToastification = await import('vue-toastification')
      const useToast = vueToastification.useToast || vueToastification.default?.useToast
      
      if (useToast) {
        toastInstance = useToast()
      }
    } catch (error) {
      console.warn('Failed to initialize toast:', error)
    }
  }
}

// Initialize on client
if (import.meta.client) {
  initToast()
}

// Toast service
export const toastService = {
  async success(message: string, options?: any) {
    if (!toastInstance) await initToast()
    if (toastInstance?.success) {
      toastInstance.success(message, { timeout: 3000, ...options })
    }
  },

  async error(message: string, options?: any) {
    if (!toastInstance) await initToast()
    if (toastInstance?.error) {
      toastInstance.error(message, { timeout: 5000, ...options })
    }
  },

  async info(message: string, options?: any) {
    if (!toastInstance) await initToast()
    if (toastInstance?.info) {
      toastInstance.info(message, { timeout: 3000, ...options })
    }
  },

  async warning(message: string, options?: any) {
    if (!toastInstance) await initToast()
    if (toastInstance?.warning) {
      toastInstance.warning(message, { timeout: 4000, ...options })
    }
  },

  async clear() {
    if (!toastInstance) await initToast()
    if (toastInstance?.clear) {
      toastInstance.clear()
    }
  }
}
