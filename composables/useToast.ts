import { toastService } from '~/utils/toast'

// Fix CommonJS/ES6 compatibility issue for production build
export const useToast = () => {
  const showSuccess = async (message: string, options?: any) => {
    await toastService.success(message, options)
  }

  const showError = async (message: string, options?: any) => {
    await toastService.error(message, options)
  }

  const showInfo = async (message: string, options?: any) => {
    await toastService.info(message, options)
  }

  const showWarning = async (message: string, options?: any) => {
    await toastService.warning(message, options)
  }

  const clear = async () => {
    await toastService.clear()
  }

  return {
    showSuccess,
    showError,
    showInfo,
    showWarning,
    clear,
    toast: toastService
  }
}
