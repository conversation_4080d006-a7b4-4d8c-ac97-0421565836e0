#!bin/bash

if [[ "$T_CI_TAG" =~ ^dev_.* ]]; then
    echo "DEV DEPLOY"
    curl -X POST --fail -F token='glptt-e904db6af4e761bc2e337e0b3097c7f7dff33a6f' -F ref=main -F variables[CURRENT_IMAGE_WEB_BHCK]="$T_TAG_COMMIT" 'https://gitlab-dvkt.vivas.vn/api/v4/projects/21/trigger/pipeline' -v
fi
if [[ "$T_CI_TAG" =~ ^staging_.* ]]; then
    echo "STAG DEPLOY"
    curl -X POST --fail -F token='glptt-e904db6af4e761bc2e337e0b3097c7f7dff33a6f' -F ref=main -F variables[CURRENT_IMAGE_WEB_BHCK]="$T_TAG_COMMIT" 'https://gitlab-dvkt.vivas.vn/api/v4/projects/21/trigger/pipeline' -v
fi
if [[ "$T_CI_TAG" =~ ^pro.* ]]; then
    echo "PRO DEPLOY"
    echo $T_TAG_COMMIT
fi
