# Vue Toastification CommonJS/ES6 Fix Guide

## Vấn đ<PERSON> build production với Node.js 20.7, gặp lỗi:

```
Named export 'useToast' not found. The requested module 'vue-toastification' is a CommonJS module, which may not support all module.exports as named exports.
```

## Nguyên nhân

- **Development**: Vite xử lý CommonJS/ES6 tự động
- **Production**: Node.js build strict hơn về module imports
- **vue-toastification**: Package có thể export theo CommonJS format

## Giải pháp

### 1. Toast Service Utility

Tạo `utils/toast.ts` để handle dynamic import:

<augment_code_snippet path="utils/toast.ts" mode="EXCERPT">
```typescript
// Toast utility to handle CommonJS/ES6 compatibility issues
let toastInstance: any = null

// Initialize toast on client side
const initToast = async () => {
  if (import.meta.client && !toastInstance) {
    try {
      // Dynamic import to handle CommonJS/ES6 compatibility
      const vueToastification = await import('vue-toastification')
      const useToast = vueToastification.useToast || vueToastification.default?.useToast
      
      if (useToast) {
        toastInstance = useToast()
      }
    } catch (error) {
      console.warn('Failed to initialize toast:', error)
    }
  }
}

// Toast service
export const toastService = {
  async success(message: string, options?: any) {
    if (!toastInstance) await initToast()
    if (toastInstance?.success) {
      toastInstance.success(message, { timeout: 3000, ...options })
    }
  },
  // ... other methods
}
```
</augment_code_snippet>

### 2. Updated Composable

Cập nhật `composables/useToast.ts`:

<augment_code_snippet path="composables/useToast.ts" mode="EXCERPT">
```typescript
import { toastService } from '~/utils/toast'

// Fix CommonJS/ES6 compatibility issue for production build
export const useToast = () => {
  const showSuccess = async (message: string, options?: any) => {
    await toastService.success(message, options)
  }

  const showError = async (message: string, options?: any) => {
    await toastService.error(message, options)
  }

  // ... other methods

  return {
    showSuccess,
    showError,
    showInfo,
    showWarning,
    clear,
    toast: toastService
  }
}
```
</augment_code_snippet>

### 3. Simplified Plugin

Cập nhật `plugins/vue-toastification.client.ts`:

<augment_code_snippet path="plugins/vue-toastification.client.ts" mode="EXCERPT">
```typescript
// Simple plugin setup to avoid CommonJS/ES6 issues
import "vue-toastification/dist/index.css"

export default defineNuxtPlugin(async (nuxtApp) => {
  if (import.meta.client) {
    try {
      // Dynamic import to handle CommonJS/ES6 compatibility
      const vueToastification = await import('vue-toastification')
      const Toast = vueToastification.default || vueToastification
      const POSITION = vueToastification.POSITION
      const TYPE = vueToastification.TYPE
      
      // Configure and install toast plugin
      nuxtApp.vueApp.use(Toast, toastOptions)
    } catch (error) {
      console.warn('Failed to initialize vue-toastification:', error)
    }
  }
})
```
</augment_code_snippet>

## Cách hoạt động

### 1. Development Mode
- Vite xử lý imports tự động
- Named exports hoạt động bình thường
- Không cần dynamic import

### 2. Production Mode
- Node.js build strict về module format
- Dynamic import giải quyết compatibility
- Fallback handling cho missing exports

### 3. Client-side Only
- Toast chỉ hoạt động trên client
- Server-side rendering không cần toast
- `import.meta.client` check đảm bảo safety

## Migration Pattern

### Before (Problematic):
```typescript
import { useToast } from "vue-toastification" // ❌ Fails in production

export const useToast = () => {
  const toast = useVueToastification() // ❌ Direct import
  return { showSuccess: (msg) => toast.success(msg) }
}
```

### After (Fixed):
```typescript
// ✅ Dynamic import with fallback
const initToast = async () => {
  const vueToastification = await import('vue-toastification')
  const useToast = vueToastification.useToast || vueToastification.default?.useToast
  return useToast?.()
}

export const useToast = () => {
  return {
    showSuccess: async (msg) => {
      const toast = await initToast()
      toast?.success(msg)
    }
  }
}
```

## Usage trong Components

### Không thay đổi API:
```vue
<script setup>
const { showSuccess, showError } = useToast()

// Vẫn sử dụng như cũ
const handleSuccess = () => {
  showSuccess('Đăng nhập thành công!')
}

const handleError = () => {
  showError('Có lỗi xảy ra!')
}
</script>
```

### Async handling (optional):
```vue
<script setup>
const { showSuccess, showError } = useToast()

// Có thể await nếu cần
const handleSuccess = async () => {
  await showSuccess('Đăng nhập thành công!')
  // Do something after toast shows
}
</script>
```

## Testing

### 1. Development Test
```bash
npm run dev
# Toast should work normally
```

### 2. Production Build Test
```bash
npm run build
npm run preview
# Toast should work without CommonJS errors
```

### 3. Manual Test
1. Open browser console
2. Check for import errors
3. Test toast functionality
4. Verify no CommonJS warnings

## Benefits

### 1. Compatibility
- ✅ Works in both dev and production
- ✅ Handles CommonJS/ES6 differences
- ✅ Graceful fallback for missing exports

### 2. Performance
- ✅ Dynamic import only when needed
- ✅ Client-side only loading
- ✅ No server-side overhead

### 3. Maintainability
- ✅ Centralized toast logic
- ✅ Easy to update/replace toast library
- ✅ Consistent API across components

## Alternative Solutions

### 1. Package.json Module Resolution
```json
{
  "type": "module",
  "exports": {
    ".": {
      "import": "./dist/index.esm.js",
      "require": "./dist/index.cjs.js"
    }
  }
}
```

### 2. Vite Config
```typescript
// vite.config.ts
export default {
  optimizeDeps: {
    include: ['vue-toastification']
  },
  ssr: {
    noExternal: ['vue-toastification']
  }
}
```

### 3. Nuxt Config
```typescript
// nuxt.config.ts
export default {
  build: {
    transpile: ['vue-toastification']
  }
}
```

## Troubleshooting

### 1. Still getting CommonJS errors?
- Check if dynamic import is working
- Verify `import.meta.client` usage
- Test with different Node.js versions

### 2. Toast not showing?
- Check browser console for errors
- Verify CSS is loaded
- Test toast service initialization

### 3. Build fails?
- Update to latest vue-toastification
- Try alternative toast libraries
- Use manual toast implementation

## Conclusion

Dynamic import approach giải quyết hoàn toàn CommonJS/ES6 compatibility issue trong production build, đồng thời maintain API consistency và performance. 🚀
