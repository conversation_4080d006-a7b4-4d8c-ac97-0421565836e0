# Hướng dẫn Auth Modal System - BIC

## Tổng quan

Hệ thống Auth Modal đã được tái cấu trúc hoàn toàn để sử dụng BaseModal component và tích hợp với Header menu. Hệ thống bao gồm:

- ✅ **LoginModal**: Modal đăng nhập với BaseModal
- ✅ **RegisterModal**: Modal đăng ký với BaseModal  
- ✅ **ForgotPasswordModal**: Modal quên mật khẩu với BaseModal
- ✅ **Auth Store**: Pinia store quản lý authentication state
- ✅ **Header Integration**: Menu Tài khoản tích hợp với auth modals

## Cấu trúc Files

```
components/
├── BaseModal.vue              # Base modal component
├── Header.vue                 # Header với menu Tài khoản
└── auth/
    ├── LoginModal.vue         # Modal đăng nhập
    ├── RegisterModal.vue      # Modal đăng ký
    └── ForgotPasswordModal.vue # Modal quên mật khẩu

stores/
└── auth.ts                    # Pinia auth store

plugins/
└── auth.client.ts             # Plugin khởi tạo auth
```

## Tính năng chính

### 1. Header Menu Integration

#### Khi chưa đăng nhập:
- Click icon Tài khoản → Hiển thị dropdown với:
  - **Đăng nhập** → Mở LoginModal
  - **Đăng ký** → Mở RegisterModal

#### Khi đã đăng nhập:
- Click icon Tài khoản → Hiển thị dropdown với:
  - **Thông tin người dùng** (tên + email)
  - **Thông tin cá nhân** → Điều hướng đến `/account/profile`
  - **Đổi mật khẩu** → Mở ChangePasswordModal
  - **Đăng xuất** → Logout và clear auth state

### 2. Auth Modals

#### LoginModal
- ✅ Sử dụng BaseModal với title "Đăng nhập"
- ✅ Form validation với Zod + Vee-validate
- ✅ Email field (required, email format)
- ✅ Password field với show/hide toggle (auto-hide sau 5s)
- ✅ Nút show password có padding phù hợp
- ✅ Links chuyển đổi: "Đăng ký", "Quên mật khẩu?"
- ✅ Toast notifications cho success/error
- ✅ Tích hợp với Auth Store

#### RegisterModal
- ✅ Sử dụng BaseModal với title "Đăng ký"
- ✅ Form validation với Zod + Vee-validate
- ✅ Họ và tên field (required, min 2 chars)
- ✅ Email field (required, email format)
- ✅ Password field với show/hide toggle
- ✅ Confirm Password field với show/hide toggle
- ✅ Password matching validation
- ✅ Link chuyển về: "Đăng nhập"
- ✅ Toast notifications
- ✅ Tích hợp với Auth Store

#### ForgotPasswordModal
- ✅ Sử dụng BaseModal với title "Quên mật khẩu"
- ✅ Form validation với Zod + Vee-validate
- ✅ Email field (required, email format)
- ✅ Description text hướng dẫn
- ✅ Link quay lại: "← Quay lại đăng nhập"
- ✅ Toast notifications
- ✅ Tích hợp với Auth Store

### 3. Auth Store (Pinia)

#### State Management:
```typescript
interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}
```

#### Actions:
- `initAuth()`: Khởi tạo từ localStorage
- `login(credentials)`: Đăng nhập
- `register(userData)`: Đăng ký
- `logout()`: Đăng xuất
- `forgotPassword(email)`: Quên mật khẩu
- `changePassword(data)`: Đổi mật khẩu
- `updateProfile(userData)`: Cập nhật profile

#### Getters:
- `currentUser`: User hiện tại
- `isLoggedIn`: Trạng thái đăng nhập
- `userEmail`: Email người dùng
- `userFullName`: Tên đầy đủ

## Cách sử dụng

### 1. Test Authentication Flow

#### Đăng nhập:
1. Click icon Tài khoản trên header
2. Chọn "Đăng nhập"
3. Nhập thông tin test:
   - **Email**: <EMAIL>
   - **Password**: password123
4. Click "Đăng nhập"

#### Đăng ký:
1. Từ LoginModal, click "Đăng ký"
2. Hoặc từ Header menu → "Đăng ký"
3. Điền form đăng ký
4. Sau khi thành công → tự động chuyển về LoginModal

#### Quên mật khẩu:
1. Từ LoginModal, click "Quên mật khẩu?"
2. Nhập email
3. Click "Gửi liên kết"
4. Sau khi thành công → tự động chuyển về LoginModal

### 2. Modal Switching Flow

```
LoginModal ←→ RegisterModal
     ↓
ForgotPasswordModal
     ↓
LoginModal
```

### 3. Sử dụng Auth Store trong Components

```vue
<script setup>
import { useAuthStore } from '~/stores/auth'

const authStore = useAuthStore()

// Check authentication
if (authStore.isLoggedIn) {
  console.log('User:', authStore.currentUser)
}

// Login
await authStore.login({ email, password })

// Logout
authStore.logout()
</script>
```

## Styling & UI

### BaseModal Styling:
- Header: `bg-[#0066B3]` với title trắng 28px
- Body: Padding `py-[40px] pt-[28px] px-5`
- Size: `lg` (max-width: 855px)
- Shadow: `shadow-[0px_4px_4px_0px_rgba(0,0,0,0.15)]`

### Form Fields:
- Height: `h-[54px]`
- Padding: `px-[14px]`
- Border: `border-[#E9ECEE]` → `focus:border-[#0066B3]`
- Border radius: `rounded-lg`
- Placeholder: `text-[#919EAB]`

### Buttons:
- Primary: `bg-[#0D68B2]` → `hover:bg-[#0056a3]`
- Height: `h-[52px]`
- Border radius: `rounded-[14.613px]`
- Shadow: `shadow-[0px_10.96px_36.532px_0px_rgba(0,0,0,0.15)]`

### Password Toggle:
- Position: `absolute right-3 top-1/2`
- Padding: `p-2` với `hover:bg-gray-100`
- Border radius: `rounded-full`
- Icon size: `w-5 h-5`

## Validation Rules

### Email:
- Required: "Email là bắt buộc"
- Format: "Email không hợp lệ"

### Password:
- Required: "Mật khẩu là bắt buộc"
- Min length (register): 6 chars

### Full Name:
- Required: "Họ và tên là bắt buộc"
- Min length: 2 chars

### Confirm Password:
- Required: "Xác nhận mật khẩu là bắt buộc"
- Match: "Mật khẩu xác nhận không khớp"

## Security Features

1. **Auto-hide password**: Tự động ẩn sau 5 giây
2. **Form validation**: Client-side với Zod
3. **Loading states**: Prevent double submission
4. **Error handling**: Graceful error messages
5. **Token management**: Secure localStorage storage
6. **Auto logout**: Clear state on logout

## Browser Support

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## Performance

- Lazy loading modals
- Optimized re-renders
- Efficient state management
- Minimal bundle impact

## Accessibility

- Keyboard navigation
- Screen reader support
- Focus management
- ARIA labels
- Color contrast compliance
