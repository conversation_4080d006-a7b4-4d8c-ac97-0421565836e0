<template>
  <div class="bg-[#ffffff] relative min-h-screen">
    <!-- Title Section -->
    <div class="flex flex-col items-center gap-[9px] pt-[115px] mb-[40px]">
      <div class="flex flex-col items-center gap-10">
        <div class="font-bold text-[40px] leading-[1.2] text-center text-neutral-700 max-w-4xl">
          <p><PERSON><PERSON>o hiể<PERSON> bắt buộc trách nhiệm dân sự của chủ xe ô tô</p>
        </div>
      </div>
      <div class="font-normal text-[16px] leading-[1.2] text-center text-neutral-700">
        <p>Vui lòng cung cấp đầy đủ các thông tin dưới đây để tiếp tục.</p>
      </div>
    </div>

    <!-- Stepper Navigation -->
    <div class="flex items-center justify-center mb-[40px]">
      <div class="flex items-center gap-0 relative">
        <!-- Step 1 -->
        <div class="flex flex-col items-center gap-2 w-[143px]">
          <div class="bg-[#3079ff] flex items-center justify-center w-[60px] h-[60px] rounded-[30px] px-4 py-3">
            <span class="font-bold text-[28px] leading-[1.2] text-center text-[#ffffff]">1</span>
          </div>
          <div class="text-center w-full">
            <p class="text-[16px] leading-[1.2] font-bold text-[#3079ff]">
              Khai báo thông tin mua bảo hiểm
            </p>
          </div>
        </div>

        <!-- Line 1 -->
        <div class="w-[257px] h-0 relative">
          <div class="absolute top-[-2px] left-0 right-0 bottom-0">
            <svg width="257" height="4" viewBox="0 0 257 4" fill="none">
              <path d="M0 2H257" stroke="#3079ff" stroke-width="2"/>
            </svg>
          </div>
        </div>

        <!-- Step 2 -->
        <div class="flex flex-col items-center gap-2 w-[143px]">
          <div class="bg-[#3079ff] flex items-center justify-center w-[60px] h-[60px] rounded-[30px] px-4 py-3">
            <span class="font-bold text-[28px] leading-[1.2] text-center text-[#ffffff]">2</span>
          </div>
          <div class="text-center w-full">
            <p class="text-[16px] leading-[1.2] font-bold text-[#3079ff]">Xác nhận thông tin</p>
          </div>
        </div>

        <!-- Line 2 -->
        <div class="w-[253px] h-0 relative">
          <div class="absolute top-[-2px] left-0 right-0 bottom-0">
            <svg width="253" height="4" viewBox="0 0 253 4" fill="none">
              <path d="M0 2H253" stroke="#3079ff" stroke-width="2"/>
            </svg>
          </div>
        </div>

        <!-- Step 3 -->
        <div class="flex flex-col items-center gap-2 w-[143px]">
          <div class="bg-[#3079ff] flex items-center justify-center w-[60px] h-[60px] rounded-[30px] px-4 py-3">
            <span class="font-bold text-[28px] leading-[1.2] text-center text-[#ffffff]">3</span>
          </div>
          <div class="text-center w-full">
            <p class="text-[16px] leading-[1.2] font-bold text-[#3079ff]">Thanh toán</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Content Area -->
    <div class="flex justify-center pb-[200px]">
      <div class="bg-[#ffffff] border border-[#e9ecee] border-solid rounded-lg w-[1140px] min-h-[1478px] p-8">
        <!-- Step 3 Content -->
        <div class="flex flex-col items-center justify-center h-full min-h-[400px]">
          <div class="text-center space-y-6">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            
            <h2 class="text-3xl font-bold text-gray-800">Step 3 - Thanh toán</h2>
            
            <p class="text-lg text-gray-600 max-w-md">
              Trang này sẽ hiển thị các phương thức thanh toán và xử lý giao dịch mua bảo hiểm.
            </p>
            
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-lg">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-yellow-800 font-medium">Đang phát triển</span>
              </div>
              <p class="text-yellow-700 mt-1 text-sm">
                Nội dung chi tiết của bước này sẽ được bổ sung sau.
              </p>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-center gap-4 pt-8">
              <button
                @click="goBack"
                class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Quay lại
              </button>
              <button
                @click="completePayment"
                class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Hoàn tất thanh toán
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// SEO Meta
useHead({
  title: 'Bước 3: Thanh toán - BIC',
  meta: [
    {
      name: 'description',
      content: 'Thanh toán bảo hiểm bắt buộc trách nhiệm dân sự của chủ xe ô tô'
    }
  ]
})

// Router
const router = useRouter()

// Methods
const goBack = () => {
  router.push('/insurance/purchase/step-2')
}

const completePayment = () => {
  // Handle payment completion
  alert('Thanh toán thành công! (Demo)')
  router.push('/')
}
</script>

<style scoped>
/* Custom styles for step 3 */
</style>
