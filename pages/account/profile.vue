<template>
  <div class="bg-white">
    <!-- Main Content -->
    <main class="max-w-[1920px] min-h-[80vh] mx-auto px-[390px] py-10 pb-0">
      <!-- Content Container -->
      <!-- Page Title -->
      <div class="mb-12">
        <h1 class="text-3xl font-bold text-[#1A1A1A] text-center">Thông tin tài khoản</h1>  
      </div>
      <div class="bg-white border border-[#E9ECEE] rounded-lg p-8 py-56 pt-6 mb-6 max-w-[1140px]" :class="{ 'bg-white': true }">
        <!-- Loading State -->
        <div v-if="loading" class="flex items-center justify-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span class="ml-3 text-gray-600">Đang tải thông tin...</span>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="text-center py-12">
          <div class="text-red-600 mb-4">{{ error }}</div>
          <button 
            @click="retryFetch" 
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Thử lại
          </button>
        </div>

        <!-- Account Information Grid -->
        <div v-else class="space-y-6">

          <!-- Company Address -->
          <div class="flex items-center justify-between gap-2">
            <label class="text-base font-medium text-[#333333]">Địa chỉ công ty</label>
            <div class="text-base text-[#333333] font-bold">{{ accountInfo.companyAddress || 'Chưa cập nhật' }}</div>
          </div>

          <!-- Tax Number -->
          <div class="flex items-center justify-between gap-2">
            <label class="text-base font-medium text-[#333333]">Mã số thuế</label>
            <div class="text-base text-[#333333] font-bold">{{ accountInfo.taxNumber || 'Chưa cập nhật' }}</div>
          </div>

          <!-- Phone Number -->
          <div class="flex items-center justify-between gap-2">
            <label class="text-base font-medium text-[#333333]">Số điện thoại</label>
            <div class="text-base text-[#333333] font-bold">{{ accountInfo.phoneNumber || 'Chưa cập nhật' }}</div>
          </div>

          <!-- Email GCN -->
          <div class="flex items-center justify-between gap-2">
            <label class="text-base font-medium text-[#333333]">Email nhận GCN bảo hiểm</label>
            <div class="text-base text-[#333333] font-bold">{{ accountInfo.emailGcn || 'Chưa cập nhật' }}</div>
          </div>
        </div>
      </div>
    </main>
  </div>
  
</template>

<script setup lang="ts">
import { useAuthStore } from '~/stores/auth'

useHead({
  title: 'Thông tin tài khoản'
})

const { get } = useApi()
const authStore = useAuthStore()
const { showError } = useToast()

// Reactive data
const loading = ref(false)
const error = ref<string | null>(null)
const accountInfo = ref({
  id: 0,
  companyAddress: '',
  taxNumber: '',
  phoneNumber: '',
  emailGcn: ''
})

// Fetch user info from API and update store
const fetchUserInfo = async () => {
  try {
    loading.value = true
    error.value = null
    
    const response = await get('/v1/users/info')
    console.log(response.data);
    
    if (response.error) {
      throw new Error('Không thể tải thông tin tài khoản')
    }
    
    if (response.data.data) {
      // Update local data
      accountInfo.value = {
        id: response.data.id || 0,
        companyAddress: response.data.data.company_address || '',
        taxNumber: response.data.data.tax_number || '',
        phoneNumber: response.data.data.phone_number || '',
        emailGcn: response.data.data.email_gcn || response.data.data.username ||''
      }
      
      // Update auth store with latest user info
      authStore.updateProfile(response.data.data)
    }
  } catch (err: any) {
    error.value = err.message || 'Có lỗi xảy ra khi tải thông tin tài khoản'
    showError(error.value)
    
    // Fallback to store data if API fails
    const user = authStore.currentUser
    if (user) {
      accountInfo.value = {
        id: user.id || 0,
        companyAddress: user.company_address || '',
        taxNumber: user.tax_number || '',
        phoneNumber: user.phone_number || '',
        emailGcn: user.email_gcn || ''
      }
    }
  } finally {
    loading.value = false
  }
}

// Retry function
const retryFetch = () => {
  fetchUserInfo()
}

// Initialize auth and fetch data on mount
onMounted(() => {
  authStore.initAuth()
  if (authStore.isLoggedIn) {
    fetchUserInfo()
  } else {
    // Redirect to login if not authenticated
    navigateTo('/auth/login')
  }
})

</script>

<style scoped>
/* Custom styles to match Figma design exactly */

header {
  height: auto;
}

main {
  flex: 1;
}

footer {
  height: 121.75px;
  position: relative;
}

/* Ensure proper layout spacing */
.max-w-\[1920px\] {
  max-width: 1920px;
}

/* Shadow effects */
header {
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.15);
}
</style>