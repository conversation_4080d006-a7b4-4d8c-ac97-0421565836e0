stages:
  - build
  - package
  - deploy

compile:
  stage: build
  image: oven/bun:1.2.18
  tags:
    - vuejs
  only:
    - tags
  except:
    - master
  before_script:
    - export T_CI_TAG=$CI_COMMIT_TAG
  script:
    - echo $CI_ENV >> .env
    - echo $CI_API_DEV >> .env
    - echo $CI_API_KEY_DEV >> .env
    - export NODE_ENV='production'
    - echo $NODE_ENV
    - bun install
    - bun run build --mode production
  artifacts:
    paths:
      - .output
      
docker_image:
  stage: package
  image: docker:23.0.0-dind
  tags:
    - vuejs
  only:
    - tags
  except:
    - master
  variables:
    TAG_COMMIT: registry.vivas.vn/bhck/landing:$CI_COMMIT_SHA
  script:
    - echo "package the code..."
    - echo "5G4Ro0cWqX2oP29JbxxNMwipNmT6Hhmy" | docker login registry.vivas.vn --username 'robot$bhck+build' --password-stdin
    - docker build -t $TAG_COMMIT .
    - docker push $TAG_COMMIT



deploy:
  stage: deploy
  image: curlimages/curl
  variables:
    TAG_COMMIT: registry.vivas.vn/bhck/landing:$CI_COMMIT_SHA
  tags:
    - vuejs # Tags for runner
  only:
    - tags
  except:
    - master
  script:
    - export T_CI_TAG=$CI_COMMIT_TAG
    - export T_TAG_COMMIT=CURRENT_IMAGE=$TAG_COMMIT
    - sh .deploy.sh





