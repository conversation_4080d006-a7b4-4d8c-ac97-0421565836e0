{"name": "bic-fe", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxt/devtools": "latest", "@nuxtjs/tailwindcss": "^6.14.0", "nuxt": "^3.12.2"}, "dependencies": {"@pinia/nuxt": "^0.5.1", "@vee-validate/zod": "^4.15.1", "pinia": "^2.1.7", "vee-validate": "^4.15.1", "vue-toastification": "^2.0.0-rc.5", "zod": "^3.25.76"}}